{"global_stats": {"mean": 0.4587211608886719, "std": 0.01235926803201437, "median": 0.45457905530929565, "q25": 0.45457905530929565, "q75": 0.45457905530929565, "q90": 0.47034531831741333, "q95": 0.4875667035579681, "q99": 0.5143177771568298, "q99_5": 0.5217202126979827, "q99_9": 0.5403472290039076, "min": 0.45457005500793457, "max": 0.5814226865768433, "count": 10800}, "threshold_info": {"threshold": 0.5, "threshold_method": "fixed", "threshold_value": 0.5, "description": "Fixed threshold at 0.5 for anomaly detection"}, "model_type": "gan_discriminator"}