import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np

import onnx

# print(torch.__version__)
# print(onnx.__version__)
# print(np.__version__)

# 2.2.0+cu121
# 1.16.1
# 1.24.4

# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='mean') * x.size(1)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss


# 计算新颖性评分
def calculate_novelty_score(recon_x, x, mu, logvar, recon_weight=0.5, kl_weight=0.5):
    recon_loss = F.mse_loss(recon_x.view_as(x), x, reduction='none').mean(dim=tuple(range(1, x.dim())))
    kl_divergence = -0.5 * torch.mean(1 + logvar - mu.pow(2) - logvar.exp(), dim=1)
    novelty_score = recon_weight * recon_loss + kl_weight * kl_divergence
    return novelty_score

# VAE模型
latent_dim = 64        # 隐变量维度
input_dim = 1 * 2048   # 输入层维度
inter_dim = 512        # 过渡层维度


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, latent_dim * 2),   # 同时输出：隐空间的均值向量mu、对数方差向量log_var
        )

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, input_dim),
            nn.Sigmoid(),
        )

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        epsilon = torch.randn_like(mu)                 # 生成与mu形状相同的张量，其元素是从标准正态分布N(0,1)中随机抽取的
        return mu + epsilon * torch.exp(logvar / 2)    # 生成隐变量z，该变量服从以mu为均值，exp(logvar/2)为标准差的正态分布

    def forward(self, x):
        org_size = x.size()     # 获取输入x的原始尺寸
        batch = org_size[0]     # 获取批次大小
        x = x.view(batch, -1)   # 将x展平为二维向量，便于处理

        h = self.encoder(x)                  # 编码器处理输入x
        mu, logvar = h.chunk(2, dim=1)       # 将编码器输出分为两部分：均值mu和对数方差logvar

        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decoder(z).view(org_size)   # 解码器重构输入，并恢复原始尺寸

        return calculate_novelty_score(recon_x, x, mu, logvar).view(-1,1), vae_loss(recon_x, x,mu, logvar)

# 初始化模型、优化器
vae = VAE(input_dim, inter_dim, latent_dim)
print(vae(torch.randn(32,2048)))


# 保存成ONNX
x, l = torch.randn(2, 2048, requires_grad=True)


print(x)
print(l)
# 保存整个模型
torch.save(vae, 'vae.pth')


# torch.onnx.export(vae,               # model being run
#                   x,             # 模型输入
#                   "VAE.onnx",   # 模型存储位置
#                   export_params=True,        # store the trained parameter weights inside the model file
#                   opset_version=10,   #     the ONNX version to export the model to
#                   do_constant_folding=True,  # whether to execute constant folding for optimization
#                   input_names = ['input'],   # the model's input names
#                   output_names = ['output'], # the model's output names
#                   # variable length axes
#                   dynamic_axes={'input' : {0 : 'batch_size'},    
#                                 'output' : {0 : 'batch_size'}})


