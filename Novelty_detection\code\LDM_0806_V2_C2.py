import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math
from collections import Counter

# python -m visdom.server

def plot_error_distribution_improved(normal_scores, anomaly_scores, global_threshold=None,
                                   condition_thresholds=None, title="Reconstruction Error Distribution"):
    """
    改进的重构误差分布绘制函数

    Args:
        normal_scores: 正常数据的误差分数
        anomaly_scores: 异常数据的误差分数
        global_threshold: 全局阈值
        condition_thresholds: 工况特定阈值字典
        title: 图表标题
    """
    # 计算合适的bin数量（基于数据量和分布范围）
    n_samples = len(normal_scores) + len(anomaly_scores)
    n_bins = min(50, max(20, int(np.sqrt(n_samples))))  # 自适应bin数量

    # 计算统一的bin边界（确保柱子宽度一致）
    min_score = min(min(normal_scores), min(anomaly_scores))
    max_score = max(max(normal_scores), max(anomaly_scores))
    common_bins = np.linspace(min_score, max_score, n_bins + 1)

    # 创建图表
    plt.figure(figsize=(12, 8))

    # 绘制直方图（使用密度归一化以便更好地比较）
    n_counts, bins, patches = plt.hist(normal_scores, bins=common_bins, alpha=0.7, color='blue',
                                      edgecolor='black', label=f'Normal Data (n={len(normal_scores)})',
                                      density=True)
    a_counts, bins, patches = plt.hist(anomaly_scores, bins=common_bins, alpha=0.7, color='red',
                                      edgecolor='black', label=f'Anomaly Data (n={len(anomaly_scores)})',
                                      density=True)

    # 添加核密度估计曲线（提升专业性）
    from scipy.stats import gaussian_kde
    x_grid = np.linspace(min_score, max_score, 200)

    if len(normal_scores) > 1:
        kde_normal = gaussian_kde(normal_scores, bw_method=0.3)
        plt.plot(x_grid, kde_normal(x_grid), color='darkblue', linewidth=2,
                linestyle='--', alpha=0.8, label='Normal KDE')

    if len(anomaly_scores) > 1:
        kde_anomaly = gaussian_kde(anomaly_scores, bw_method=0.3)
        plt.plot(x_grid, kde_anomaly(x_grid), color='darkred', linewidth=2,
                linestyle='--', alpha=0.8, label='Anomaly KDE')

    # 添加全局阈值线
    if global_threshold is not None:
        plt.axvline(global_threshold, color='black', linestyle='dashed', linewidth=2,
                   label=f'Global Threshold = {global_threshold:.4f}')

    # 添加工况特定阈值
    if condition_thresholds:
        threshold_values = list(condition_thresholds.values())
        if threshold_values:
            # 显示工况特定阈值范围
            min_threshold = min(threshold_values)
            max_threshold = max(threshold_values)
            plt.axvspan(min_threshold, max_threshold, alpha=0.2, color='green',
                       label=f'Condition Thresholds Range [{min_threshold:.4f}, {max_threshold:.4f}]')

            # 显示每个工况特定阈值线
            unique_thresholds = sorted(set(threshold_values))
            colors = ['orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
            for i, threshold in enumerate(unique_thresholds):
                # 统计使用该阈值的工况数量
                count = sum(1 for v in threshold_values if abs(v - threshold) < 1e-6)
                color = colors[i % len(colors)]
                plt.axvline(threshold, color=color, linestyle=':', linewidth=1.5, alpha=0.8,
                           label=f'Condition Threshold {threshold:.4f} ({count} conditions)')

    # 设置图表属性
    plt.xlabel('Reconstruction Error', fontsize=12)
    plt.ylabel('Density', fontsize=12)
    plt.title(title, fontsize=14, fontweight='bold')
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)

    # 添加统计信息文本框
    stats_text = f'Normal: μ={np.mean(normal_scores):.4f}, σ={np.std(normal_scores):.4f}\n'
    stats_text += f'Anomaly: μ={np.mean(anomaly_scores):.4f}, σ={np.std(anomaly_scores):.4f}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    return plt.gcf()

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

# AE可视化窗口
AE_train_wind = Visdom()
AE_train_wind.line([0.0], [0.0], win='AE_train', opts=dict(title='AE Training Loss', legend=['Loss']))
AE_recon_loss = Visdom()
AE_recon_loss.line([0.0], [0.0], win='AE_recon_loss', opts=dict(title='AE_recon_loss', legend=['Recon Loss']))

# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal_1500_9 = scio.loadmat('..\\dataset\\detection_normal_1500_9_train_2048.mat')
        normal_1500_9 = train_normal_1500_9['detection_normal_1500_9_train_2048']

        self.normal_data_1500_9 = normal_1500_9[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_9 = torch.from_numpy(self.normal_data_1500_9)

        self.normal_data_1500_9 = self.normal_data_1500_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_12 = scio.loadmat('..\\dataset\\detection_normal_1500_12_train_2048.mat')
        normal_1500_12 = train_normal_1500_12['detection_normal_1500_12_train_2048']

        self.normal_data_1500_12 = normal_1500_12[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_12 = torch.from_numpy(self.normal_data_1500_12)

        self.normal_data_1500_12 = self.normal_data_1500_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_15 = scio.loadmat('..\\dataset\\detection_normal_1500_15_train_2048.mat')
        normal_1500_15 = train_normal_1500_15['detection_normal_1500_15_train_2048']

        self.normal_data_1500_15 = normal_1500_15[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_15 = torch.from_numpy(self.normal_data_1500_15)

        self.normal_data_1500_15 = self.normal_data_1500_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_18 = scio.loadmat('..\\dataset\\detection_normal_1500_18_train_2048_NEW1.mat')
        normal_1500_18 = train_normal_1500_18['detection_normal_1500_18_train_2048_NEW1']

        self.normal_data_1500_18 = normal_1500_18[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_18 = torch.from_numpy(self.normal_data_1500_18)

        self.normal_data_1500_18 = self.normal_data_1500_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_9 = scio.loadmat('..\\dataset\\detection_normal_1200_9_train_2048.mat')
        normal_1200_9 = train_normal_1200_9['detection_normal_1200_9_train_2048']

        self.normal_data_1200_9 = normal_1200_9[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_9 = torch.from_numpy(self.normal_data_1200_9)

        self.normal_data_1200_9 = self.normal_data_1200_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_12 = scio.loadmat('..\\dataset\\detection_normal_1200_12_train_2048.mat')
        normal_1200_12 = train_normal_1200_12['detection_normal_1200_12_train_2048']

        self.normal_data_1200_12 = normal_1200_12[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_12 = torch.from_numpy(self.normal_data_1200_12)

        self.normal_data_1200_12 = self.normal_data_1200_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_15 = scio.loadmat('..\\dataset\\detection_normal_1200_15_train_2048_NEW1.mat')
        normal_1200_15 = train_normal_1200_15['detection_normal_1200_15_train_2048_NEW1']

        self.normal_data_1200_15 = normal_1200_15[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_15 = torch.from_numpy(self.normal_data_1200_15)

        self.normal_data_1200_15 = self.normal_data_1200_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_18 = scio.loadmat('..\\dataset\\detection_normal_1200_18_train_2048_NEW.mat')
        normal_1200_18 = train_normal_1200_18['detection_normal_1200_18_train_2048_NEW']

        self.normal_data_1200_18 = normal_1200_18[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_18 = torch.from_numpy(self.normal_data_1200_18)

        self.normal_data_1200_18 = self.normal_data_1200_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_9 = scio.loadmat('..\\dataset\\detection_normal_900_9_train_2048.mat')
        normal_900_9 = train_normal_900_9['detection_normal_900_9_train_2048']

        self.normal_data_900_9 = normal_900_9[0, 0:1843200]  # 样本数：900

        self.normal_data_900_9 = torch.from_numpy(self.normal_data_900_9)

        self.normal_data_900_9 = self.normal_data_900_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_12 = scio.loadmat('..\\dataset\\detection_normal_900_12_train_2048.mat')
        normal_900_12 = train_normal_900_12['detection_normal_900_12_train_2048']

        self.normal_data_900_12 = normal_900_12[0, 0:1843200]  # 样本数：900

        self.normal_data_900_12 = torch.from_numpy(self.normal_data_900_12)

        self.normal_data_900_12 = self.normal_data_900_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_15 = scio.loadmat('..\\dataset\\detection_normal_900_15_train_2048.mat')
        normal_900_15 = train_normal_900_15['detection_normal_900_15_train_2048']

        self.normal_data_900_15 = normal_900_15[0, 0:1843200]  # 样本数：900

        self.normal_data_900_15 = torch.from_numpy(self.normal_data_900_15)

        self.normal_data_900_15 = self.normal_data_900_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_18 = scio.loadmat('..\\dataset\\detection_normal_900_18_train_2048.mat')
        normal_900_18 = train_normal_900_18['detection_normal_900_18_train_2048']

        self.normal_data_900_18 = normal_900_18[0, 0:1843200]  # 样本数：900

        self.normal_data_900_18 = torch.from_numpy(self.normal_data_900_18)

        self.normal_data_900_18 = self.normal_data_900_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        self.x_data = [self.normal_data_1500_9, self.normal_data_1500_12, self.normal_data_1500_15,
                       self.normal_data_1500_18,
                       self.normal_data_1200_9, self.normal_data_1200_12, self.normal_data_1200_15,
                       self.normal_data_1200_18,
                       self.normal_data_900_9, self.normal_data_900_12, self.normal_data_900_15,
                       self.normal_data_900_18]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.x_data.shape[0])  # 计算标签数量
        y_data1 = 0 * np.ones(size)  # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        count_1500_9 = self.normal_data_1500_9.shape[0]
        count_1500_12 = self.normal_data_1500_12.shape[0]
        count_1500_15 = self.normal_data_1500_15.shape[0]
        count_1500_18 = self.normal_data_1500_18.shape[0]

        count_1200_9 = self.normal_data_1200_9.shape[0]
        count_1200_12 = self.normal_data_1200_12.shape[0]
        count_1200_15 = self.normal_data_1200_15.shape[0]
        count_1200_18 = self.normal_data_1200_18.shape[0]

        count_900_9 = self.normal_data_900_9.shape[0]
        count_900_12 = self.normal_data_900_12.shape[0]
        count_900_15 = self.normal_data_900_15.shape[0]
        count_900_18 = self.normal_data_900_18.shape[0]

        condition_1500_9 = [(1500, 9)] * count_1500_9  # 转速1500rpm, 压力9MPa
        condition_1500_12 = [(1500, 12)] * count_1500_12  # 转速1500rpm, 压力12MPa
        condition_1500_15 = [(1500, 15)] * count_1500_15  # 转速1500rpm, 压力15MPa
        condition_1500_18 = [(1500, 18)] * count_1500_18  # 转速1500rpm, 压力18MPa

        condition_1200_9 = [(1200, 9)] * count_1200_9  # 转速1200rpm, 压力9MPa
        condition_1200_12 = [(1200, 12)] * count_1200_12  # 转速1200rpm, 压力12MPa
        condition_1200_15 = [(1200, 15)] * count_1200_15  # 转速1200rpm, 压力15MPa
        condition_1200_18 = [(1200, 18)] * count_1200_18  # 转速1200rpm, 压力18MPa

        condition_900_9 = [(900, 9)] * count_900_9  # 转速900rpm, 压力9MPa
        condition_900_12 = [(900, 12)] * count_900_12  # 转速900rpm, 压力12MPa
        condition_900_15 = [(900, 15)] * count_900_15  # 转速900rpm, 压力15MPa
        condition_900_18 = [(900, 18)] * count_900_18  # 转速900rpm, 压力18MPa

        # 合并所有工况标签
        self.condition_data = condition_1500_9 + condition_1500_12 + condition_1500_15 + condition_1500_18 + \
                              condition_1200_9 + condition_1200_12 + condition_1200_15 + condition_1200_18 + \
                              condition_900_9 + condition_900_12 + condition_900_15 + condition_900_18

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('..\\dataset\\detection_normal_1200_15_val_2048_NEW1.mat')
        normal = val_normal['detection_normal_1200_15_val_2048_NEW1']

        self.normal_data = normal[0, 0:409600]  # 样本数：200

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('..\\dataset\\loose8067_12conditions\\detection_loose8067_1200_15_val_2048.mat')
        loose8067 = val_loose8067['detection_loose8067_1200_15_val_2048']

        self.loose8067 = loose8067[0, 0:409600]  # 样本数：200

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        # 所有验证数据都是1500rpm, 15MPa工况
        condition_normal = [(1200, 15)] * size_normal  # 正常数据工况标签
        condition_loose = [(1200, 15)] * size_loose  # 异常数据工况标签

        # 合并所有工况标签
        self.condition_data = condition_normal + condition_loose

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('..\\dataset\\detection_normal_1200_15_test_2048_NEW1.mat')
        normal = test_normal['detection_normal_1200_15_test_2048_NEW1']

        self.normal_data = normal[0, 0:409600]  # 样本数：200

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('..\\dataset\\loose8067_12conditions\\detection_loose8067_1200_15_test_2048.mat')
        loose8067 = test_loose8067['detection_loose8067_1200_15_test_2048']

        self.loose8067 = loose8067[0, 0:409600]  # 样本数：200

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        # 所有测试数据都是1500rpm, 15MPa工况
        condition_normal = [(1200, 15)] * size_normal  # 正常数据工况标签
        condition_loose = [(1200, 15)] * size_loose  # 异常数据工况标签

        # 合并所有工况标签
        self.condition_data = condition_normal + condition_loose

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)

# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)

# AE模型
latent_dim = 64  # 隐变量维度
input_dim = 1 * 2048  # 输入层维度
inter_dim = 1024  # 过渡层维度

# class AE(nn.Module):
#     def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
#         super(AE, self).__init__()
#         # 编码器
#         self.encoder_fc1 = nn.Linear(input_dim, 1024)
#         self.encoder_fc2 = nn.Linear(1024, 512)
#         self.encoder_fc3 = nn.Linear(512, 256)
#         self.encoder_fc4 = nn.Linear(256, latent_dim)
#         self.dropout = nn.Dropout(0.2)
#
#         # 解码器
#         self.decoder_fc1 = nn.Linear(latent_dim, 256)
#         self.decoder_fc2 = nn.Linear(256, 512)
#         self.decoder_fc3 = nn.Linear(512, 1024)
#         self.decoder_fc4 = nn.Linear(1024, input_dim)
#         self.sigmoid = nn.Sigmoid()
#
#         # 通用层
#         self.bn1 = nn.BatchNorm1d(1024)
#         self.bn2 = nn.BatchNorm1d(512)
#         self.bn3 = nn.BatchNorm1d(256)
#         self.bn4 = nn.BatchNorm1d(256)
#         self.bn5 = nn.BatchNorm1d(512)
#         self.bn6 = nn.BatchNorm1d(1024)
#
#     def encode(self, x):
#         x = x.view(x.size(0), -1)
#         h1 = F.relu(self.bn1(self.encoder_fc1(x)))
#         h1 = self.dropout(h1)
#         h2 = F.relu(self.bn2(self.encoder_fc2(h1)))
#         h2 = self.dropout(h2)
#         h3 = F.relu(self.bn3(self.encoder_fc3(h2)))
#         h3 = self.dropout(h3)
#         z = self.encoder_fc4(h3)
#         return z
#
#     def decode(self, z):
#         h1 = F.relu(self.bn4(self.decoder_fc1(z)))
#         h1 = self.dropout(h1)
#         h2 = F.relu(self.bn5(self.decoder_fc2(h1)))
#         h2 = self.dropout(h2)
#         h3 = F.relu(self.bn6(self.decoder_fc3(h2)))
#         h3 = self.dropout(h3)
#         return self.sigmoid(self.decoder_fc4(h3))
#
#     def forward(self, x):
#         org_size = x.size()
#         batch = org_size[0]
#         x = x.view(batch, -1)
#         # 1.编码
#         z = self.encode(x)
#         # 2.解码
#         recon_x = self.decode(z).view(org_size)
#         return recon_x


class AE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(AE, self).__init__()
        # 编码器
        self.fc1 = nn.Linear(input_dim, inter_dim)
        self.fc2 = nn.Linear(inter_dim, latent_dim)
        self.dropout = nn.Dropout(0.2)

        # 解码器
        self.fc3 = nn.Linear(latent_dim, inter_dim)
        self.fc4 = nn.Linear(inter_dim, input_dim)
        self.sigmoid = nn.Sigmoid()

    def encode(self, x):
        x = x.view(x.size(0), -1)
        h1 = F.relu(self.fc1(x))
        h1 = self.dropout(h1)
        z = self.fc2(h1)
        return z

    def decode(self, z):
        h3 = F.relu(self.fc3(z))
        h3 = self.dropout(h3)
        return self.sigmoid(self.fc4(h3))

    def forward(self, x):
        org_size = x.size()
        batch = org_size[0]
        x = x.view(batch, -1)
        # 1.编码
        z = self.encode(x)
        # 2.解码
        recon_x = self.decode(z).view(org_size)
        return recon_x


# AE损失只包含重构损失
def ae_loss(recon_x, x):
    recon_loss = F.mse_loss(recon_x, x, reduction='sum') / x.size(0)
    return recon_loss


# 训练 AE 模型
ae = AE(input_dim, inter_dim, latent_dim).to(device)
ae_optimizer = optim.Adam(ae.parameters(), lr=0.001)
ae_epochs = 3

# 初始化记录损失的列表
all_losses = {
    'total_loss': [],
    'recon_loss': []
}

for ae_epoch in range(ae_epochs):
    print(f"AE Epoch {ae_epoch} - Baseline Model (Standard AE)")
    # 将模型设置为训练模式
    ae.train()
    ae_train_loss = 0.0
    epoch_losses = {
        'total_loss': [],
        'recon_loss': []
    }

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)

        # forward
        recon_x = ae(inputs)

        # AE损失（仅重构损失）
        loss = ae_loss(recon_x, inputs)
        total_loss = loss
        ae_train_loss += total_loss.item()

        # 记录损失
        epoch_losses['total_loss'].append(total_loss.item())
        epoch_losses['recon_loss'].append(loss.item())

        # backward
        ae_optimizer.zero_grad()
        total_loss.backward()

        # update
        ae_optimizer.step()

        # 每 32 个批次打印当前总损失、当前批次索引
        if idx % 32 == 0:
            print(f"Training loss {loss: .3f} in Step {idx}")

        # training curve
        global_iter_num_train = ae_epoch * len(train_loader) + idx + 1
        AE_train_wind.line([total_loss.item()], [global_iter_num_train], win='AE_train', update='append')
        AE_recon_loss.line([loss.item()], [global_iter_num_train], win='AE_recon_loss', update='append')

    # 计算并保存每个epoch的平均损失
    for key in epoch_losses:
        epoch_avg = np.mean(epoch_losses[key])
        all_losses[key].append(epoch_avg)
        print(f"Epoch {ae_epoch} average {key}: {epoch_avg:.6f}")

    # 保存损失记录到文件
    loss_df = pd.DataFrame(all_losses)
    loss_df.to_csv('baseline_ae_training_losses.csv', index=False)

# 基线模型AE训练完成
print("\n=== AE Training Completed ===")
print("Standard AE architecture with reconstruction loss only")
print("=" * 50)


# 新颖性检测结果评估
# ROC曲线 & AUC
def evaluate_roc_auc(scores, labels):
    fpr, tpr, thresholds = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {roc_auc:0.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    # 将ROC曲线的数据保存为CSV文件
    roc_data = pd.DataFrame({'False Positive Rate': fpr, 'True Positive Rate': tpr, 'Thresholds': thresholds})
    roc_data.to_csv('roc_data.csv', index=False)

    return roc_auc


# 推理过程 - 全局统一阈值设置
ae.eval()

# 设置随机种子以确保结果可重复
seed = 42
generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
generator.manual_seed(seed)

# 基线模型：收集所有训练数据的重构误差用于全局阈值计算
all_scores = []

with torch.no_grad():
    for data in train_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # forward
        recon_x = ae(inputs)

        # 计算重构误差
        recon_loss = F.mse_loss(recon_x, inputs, reduction='none')
        recon_loss = recon_loss.view(recon_loss.size(0), -1).mean(dim=1)

        # 基线模型：收集所有重构误差用于全局阈值计算
        all_scores.extend(recon_loss.cpu().numpy())

    # 基线模型：计算全局统一阈值（99.5%分位数）
    confidence = 0.995  # 置信度

    print("====== AE Model: Global Threshold Calculation ======")

    # 计算全局阈值
    all_scores_array = np.array(all_scores)
    global_mu, global_std = norm.fit(all_scores_array)
    global_threshold = norm.ppf(confidence, loc=global_mu, scale=global_std)

    print(f"Global threshold: μ={global_mu:.6f}, σ={global_std:.6f}, threshold={global_threshold:.6f}")
    print(f"Total training samples: {len(all_scores_array)}")
    print("=" * 50)

    # 保存全局阈值信息
    threshold_data = {
        "global_stats": {
            "mu": float(global_mu),
            "std": float(global_std),
            "threshold": float(global_threshold),
            "count": len(all_scores_array)
        },
        "confidence": float(confidence),
        "model_type": "baseline_unconditional"
    }

    with open("AE_global_threshold.json", "w") as f:
        json.dump(threshold_data, f, indent=2)

    print("AE global threshold saved to 'AE_global_threshold.json'")


# 加载全局阈值信息
try:
    with open("AE_global_threshold.json", "r") as f:
        threshold_data = json.load(f)

    global_threshold = threshold_data["global_stats"]["threshold"]
    confidence = threshold_data.get("confidence", 0.995)

    print("====== Loaded AE Global Threshold ======")
    print(f"Global threshold: {global_threshold:.6f}")
    print(f"Confidence: {confidence}")
    print("=" * 45)

except FileNotFoundError:
    print("AE threshold file not found. Please run threshold calculation first.")
    # 设置默认值以防文件不存在
    global_threshold = 0.5

# 推理过程 - 全局阈值新颖性检测
ae.eval()

# 设置随机种子以确保结果可重复
seed = 42
generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
generator.manual_seed(seed)

# 收集测试结果
test_scores = []
test_labels = []

with torch.no_grad():
    for data in test_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # forward
        recon_x = ae(inputs)

        # 计算重构误差
        recon_loss = F.mse_loss(recon_x, inputs, reduction='none')
        recon_loss = recon_loss.view(recon_loss.size(0), -1).mean(dim=1)

        # 收集测试数据（不按工况分组）
        for score, label in zip(recon_loss, labels):
            test_scores.append(score.item())
            test_labels.append(label.item())

# 基线模型：使用全局阈值进行预测
print("====== AE Model: Global Threshold Anomaly Detection Results ======")

# 仅使用全局阈值进行预测
all_predictions = []  # 存储使用全局阈值判断是否为异常的结果

for score in test_scores:
    # 进行预测
    pred = score > global_threshold  # 判断当前样本的重构误差是否超过了全局阈值
    all_predictions.append(int(pred))  # 储存预测结果

# 计算性能指标
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

# 全局阈值性能
baseline_auc = evaluate_roc_auc(test_scores, test_labels)
baseline_accuracy = accuracy_score(test_labels, all_predictions)
baseline_precision = precision_score(test_labels, all_predictions)
baseline_recall = recall_score(test_labels, all_predictions)
baseline_f1 = f1_score(test_labels, all_predictions)

# 计算FPR
cm_baseline = confusion_matrix(test_labels, all_predictions)
tn_baseline, fp_baseline, fn_baseline, tp_baseline = cm_baseline.ravel()
baseline_fpr = fp_baseline / (fp_baseline + tn_baseline) if (fp_baseline + tn_baseline) > 0 else 0.0

# 打印基线模型性能
print("\n📊 AE Model Performance:")
print("=" * 50)
print(f"{'Metric':<20} {'Value':<20}")
print("=" * 50)
print(f"{'AUC':<20} {baseline_auc:<20.4f}")
print(f"{'Accuracy':<20} {baseline_accuracy:<20.4f}")
print(f"{'Precision':<20} {baseline_precision:<20.4f}")
print(f"{'Recall':<20} {baseline_recall:<20.4f}")
print(f"{'F1-Score':<20} {baseline_f1:<20.4f}")
print(f"{'FPR':<20} {baseline_fpr:<20.4f}")
print("=" * 50)

# 详细分析
print("\n🔍 AE Model Detailed Analysis:")
print("\nGlobal Threshold Results:")
cm_baseline = confusion_matrix(test_labels, all_predictions)
print("Confusion Matrix:")
print(cm_baseline)
print("Classification Report:")
print(classification_report(test_labels, all_predictions))

# 绘制重构误差分布柱状图
scores_array = np.array(test_scores)
true_labels_array = np.array(test_labels)
normal_scores = scores_array[true_labels_array == 0]
anomaly_scores = scores_array[true_labels_array == 1]

# 使用改进的绘图函数，但只显示全局阈值
fig = plot_error_distribution_improved(
    normal_scores=normal_scores,
    anomaly_scores=anomaly_scores,
    global_threshold=global_threshold,
    condition_thresholds=None,  # 基线模型：不显示工况特定阈值
    title='AE Global Threshold Reconstruction Error Distribution'
)
plt.show()

print(f"\n✨ AE Model Implementation Complete!")
print(f" Global threshold: {global_threshold:.6f}")
print(f" F1-Score: {baseline_f1:.4f}")

# 保存重构误差分布图的数据
print("\n📊 Saving reconstruction error distribution data...")

# 计算直方图数据
n_samples = len(normal_scores) + len(anomaly_scores)
n_bins = min(50, max(20, int(np.sqrt(n_samples))))
min_score = min(min(normal_scores), min(anomaly_scores))
max_score = max(max(normal_scores), max(anomaly_scores))
common_bins = np.linspace(min_score, max_score, n_bins + 1)

# 计算直方图统计
normal_hist_counts, _ = np.histogram(normal_scores, bins=common_bins, density=True)
anomaly_hist_counts, _ = np.histogram(anomaly_scores, bins=common_bins, density=True)
bin_centers = (common_bins[:-1] + common_bins[1:]) / 2  # 计算bin中心点

# 计算核密度估计数据
from scipy.stats import gaussian_kde
x_grid = np.linspace(min_score, max_score, 200)

normal_kde_values = None
anomaly_kde_values = None
if len(normal_scores) > 1:
    kde_normal = gaussian_kde(normal_scores, bw_method=0.3)
    normal_kde_values = kde_normal(x_grid)

if len(anomaly_scores) > 1:
    kde_anomaly = gaussian_kde(anomaly_scores, bw_method=0.3)
    anomaly_kde_values = kde_anomaly(x_grid)

# 准备保存的数据字典
distribution_data = {
    'metadata': {
        'total_samples': n_samples,
        'normal_samples': len(normal_scores),
        'anomaly_samples': len(anomaly_scores),
        'n_bins': n_bins,
        'score_range': [float(min_score), float(max_score)],
        'global_threshold': float(global_threshold) if global_threshold is not None else None
    },
    'histogram': {
        'bin_edges': common_bins.tolist(),
        'bin_centers': bin_centers.tolist(),
        'normal_density': normal_hist_counts.tolist(),
        'anomaly_density': anomaly_hist_counts.tolist()
    },
    'kde': {
        'x_grid': x_grid.tolist(),
        'normal_kde': normal_kde_values.tolist() if normal_kde_values is not None else None,
        'anomaly_kde': anomaly_kde_values.tolist() if anomaly_kde_values is not None else None
    },
    'raw_scores': {
        'normal_scores': normal_scores.tolist(),
        'anomaly_scores': anomaly_scores.tolist()
    },
    'thresholds': {
        'global_threshold': float(global_threshold) if global_threshold is not None else None,
        'condition_thresholds': {},  # 基线模型：无工况特定阈值
        'unique_condition_thresholds': []  # 基线模型：无工况特定阈值
    },
    'statistics': {
        'normal': {
            'mean': float(np.mean(normal_scores)),
            'std': float(np.std(normal_scores)),
            'min': float(np.min(normal_scores)),
            'max': float(np.max(normal_scores))
        },
        'anomaly': {
            'mean': float(np.mean(anomaly_scores)),
            'std': float(np.std(anomaly_scores)),
            'min': float(np.min(anomaly_scores)),
            'max': float(np.max(anomaly_scores))
        }
    }
}

# 保存为JSON文件
import json
with open('AE_reconstruction_error_distribution_data.json', 'w') as f:
    json.dump(distribution_data, f, indent=2)

# 基线模型：保存为CSV文件（便于Excel等软件使用）
import pandas as pd

# 直方图数据
hist_df = pd.DataFrame({
    'bin_centers': bin_centers,
    'bin_edges_left': common_bins[:-1],
    'bin_edges_right': common_bins[1:],
    'normal_density': normal_hist_counts,
    'anomaly_density': anomaly_hist_counts
})
hist_df.to_csv('AE_histogram_data.csv', index=False)

# KDE数据
kde_df = pd.DataFrame({
    'x_grid': x_grid,
    'normal_kde': normal_kde_values if normal_kde_values is not None else [None] * len(x_grid),
    'anomaly_kde': anomaly_kde_values if anomaly_kde_values is not None else [None] * len(x_grid)
})
kde_df.to_csv('AE_kde_data.csv', index=False)

# 原始分数数据
max_len = max(len(normal_scores), len(anomaly_scores))
normal_padded = list(normal_scores) + [None] * (max_len - len(normal_scores))
anomaly_padded = list(anomaly_scores) + [None] * (max_len - len(anomaly_scores))

scores_df = pd.DataFrame({
    'normal_scores': normal_padded,
    'anomaly_scores': anomaly_padded
})
scores_df.to_csv('AE_raw_scores_data.csv', index=False)

# 基线模型：阈值数据（仅全局阈值）
thresholds_data = []
if global_threshold is not None:
    thresholds_data.append({'type': 'global', 'value': global_threshold, 'condition': 'all'})

if thresholds_data:
    thresholds_df = pd.DataFrame(thresholds_data)
    thresholds_df.to_csv('AE_thresholds_data.csv', index=False)

print("✅ AE model distribution data saved successfully:")
print("  📄 AE_reconstruction_error_distribution_data.json - Complete data in JSON format")
print("  📊 AE_histogram_data.csv - Histogram bin data")
print("  📈 AE_kde_data.csv - Kernel density estimation data")
print("  📋 AE_raw_scores_data.csv - Raw reconstruction error scores")
print("  🎯 AE_thresholds_data.csv - Global threshold value")
print("  💡 Use these files with other plotting software (e.g., Origin, MATLAB, R, etc.)")

