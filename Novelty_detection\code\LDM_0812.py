import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math
from collections import Counter

# python -m visdom.server

def plot_error_distribution_improved(normal_scores, anomaly_scores, global_threshold=None,
                                   condition_thresholds=None, title="Reconstruction Error Distribution"):
    """
    改进的重构误差分布绘制函数

    Args:
        normal_scores: 正常数据的误差分数
        anomaly_scores: 异常数据的误差分数
        global_threshold: 全局阈值
        condition_thresholds: 工况特定阈值字典
        title: 图表标题
    """
    # 计算合适的bin数量（基于数据量和分布范围）
    n_samples = len(normal_scores) + len(anomaly_scores)
    n_bins = min(50, max(20, int(np.sqrt(n_samples))))  # 自适应bin数量

    # 计算统一的bin边界（确保柱子宽度一致）
    min_score = min(min(normal_scores), min(anomaly_scores))
    max_score = max(max(normal_scores), max(anomaly_scores))
    common_bins = np.linspace(min_score, max_score, n_bins + 1)

    # 创建图表
    plt.figure(figsize=(12, 8))

    # 绘制直方图（使用密度归一化以便更好地比较）
    n_counts, bins, patches = plt.hist(normal_scores, bins=common_bins, alpha=0.7, color='blue',
                                      edgecolor='black', label=f'Normal Data (n={len(normal_scores)})',
                                      density=True)
    a_counts, bins, patches = plt.hist(anomaly_scores, bins=common_bins, alpha=0.7, color='red',
                                      edgecolor='black', label=f'Anomaly Data (n={len(anomaly_scores)})',
                                      density=True)

    # 添加核密度估计曲线（提升专业性）
    from scipy.stats import gaussian_kde
    x_grid = np.linspace(min_score, max_score, 200)

    if len(normal_scores) > 1:
        kde_normal = gaussian_kde(normal_scores, bw_method=0.3)
        plt.plot(x_grid, kde_normal(x_grid), color='darkblue', linewidth=2,
                linestyle='--', alpha=0.8, label='Normal KDE')

    if len(anomaly_scores) > 1:
        kde_anomaly = gaussian_kde(anomaly_scores, bw_method=0.3)
        plt.plot(x_grid, kde_anomaly(x_grid), color='darkred', linewidth=2,
                linestyle='--', alpha=0.8, label='Anomaly KDE')

    # 添加全局阈值线
    if global_threshold is not None:
        plt.axvline(global_threshold, color='black', linestyle='dashed', linewidth=2,
                   label=f'Global Threshold = {global_threshold:.4f}')

    # 添加工况特定阈值
    if condition_thresholds:
        threshold_values = list(condition_thresholds.values())
        if threshold_values:
            # 显示工况特定阈值范围
            min_threshold = min(threshold_values)
            max_threshold = max(threshold_values)
            plt.axvspan(min_threshold, max_threshold, alpha=0.2, color='green',
                       label=f'Condition Thresholds Range [{min_threshold:.4f}, {max_threshold:.4f}]')

            # 显示每个工况特定阈值线
            unique_thresholds = sorted(set(threshold_values))
            colors = ['orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
            for i, threshold in enumerate(unique_thresholds):
                # 统计使用该阈值的工况数量
                count = sum(1 for v in threshold_values if abs(v - threshold) < 1e-6)
                color = colors[i % len(colors)]
                plt.axvline(threshold, color=color, linestyle=':', linewidth=1.5, alpha=0.8,
                           label=f'Condition Threshold {threshold:.4f} ({count} conditions)')

    # 设置图表属性
    plt.xlabel('Reconstruction Error', fontsize=12)
    plt.ylabel('Density', fontsize=12)
    plt.title(title, fontsize=14, fontweight='bold')
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)

    # 添加统计信息文本框
    stats_text = f'Normal: μ={np.mean(normal_scores):.4f}, σ={np.std(normal_scores):.4f}\n'
    stats_text += f'Anomaly: μ={np.mean(anomaly_scores):.4f}, σ={np.std(anomaly_scores):.4f}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    return plt.gcf()

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

# GAN可视化窗口
GAN_train_wind = Visdom()
GAN_train_wind.line([0.0], [0.0], win='GAN_train', opts=dict(title='GAN Training Loss', legend=['G_Loss', 'D_Loss']))

# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal_1500_9 = scio.loadmat('..\\dataset\\detection_normal_1500_9_train_2048.mat')
        normal_1500_9 = train_normal_1500_9['detection_normal_1500_9_train_2048']

        self.normal_data_1500_9 = normal_1500_9[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_9 = torch.from_numpy(self.normal_data_1500_9)

        self.normal_data_1500_9 = self.normal_data_1500_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_12 = scio.loadmat('..\\dataset\\detection_normal_1500_12_train_2048.mat')
        normal_1500_12 = train_normal_1500_12['detection_normal_1500_12_train_2048']

        self.normal_data_1500_12 = normal_1500_12[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_12 = torch.from_numpy(self.normal_data_1500_12)

        self.normal_data_1500_12 = self.normal_data_1500_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_15 = scio.loadmat('..\\dataset\\detection_normal_1500_15_train_2048.mat')
        normal_1500_15 = train_normal_1500_15['detection_normal_1500_15_train_2048']

        self.normal_data_1500_15 = normal_1500_15[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_15 = torch.from_numpy(self.normal_data_1500_15)

        self.normal_data_1500_15 = self.normal_data_1500_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_18 = scio.loadmat('..\\dataset\\detection_normal_1500_18_train_2048_NEW1.mat')
        normal_1500_18 = train_normal_1500_18['detection_normal_1500_18_train_2048_NEW1']

        self.normal_data_1500_18 = normal_1500_18[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_18 = torch.from_numpy(self.normal_data_1500_18)

        self.normal_data_1500_18 = self.normal_data_1500_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_9 = scio.loadmat('..\\dataset\\detection_normal_1200_9_train_2048.mat')
        normal_1200_9 = train_normal_1200_9['detection_normal_1200_9_train_2048']

        self.normal_data_1200_9 = normal_1200_9[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_9 = torch.from_numpy(self.normal_data_1200_9)

        self.normal_data_1200_9 = self.normal_data_1200_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_12 = scio.loadmat('..\\dataset\\detection_normal_1200_12_train_2048.mat')
        normal_1200_12 = train_normal_1200_12['detection_normal_1200_12_train_2048']

        self.normal_data_1200_12 = normal_1200_12[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_12 = torch.from_numpy(self.normal_data_1200_12)

        self.normal_data_1200_12 = self.normal_data_1200_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_15 = scio.loadmat('..\\dataset\\detection_normal_1200_15_train_2048.mat')
        normal_1200_15 = train_normal_1200_15['detection_normal_1200_15_train_2048']

        self.normal_data_1200_15 = normal_1200_15[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_15 = torch.from_numpy(self.normal_data_1200_15)

        self.normal_data_1200_15 = self.normal_data_1200_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_18 = scio.loadmat('..\\dataset\\detection_normal_1200_18_train_2048_NEW.mat')
        normal_1200_18 = train_normal_1200_18['detection_normal_1200_18_train_2048_NEW']

        self.normal_data_1200_18 = normal_1200_18[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_18 = torch.from_numpy(self.normal_data_1200_18)

        self.normal_data_1200_18 = self.normal_data_1200_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_9 = scio.loadmat('..\\dataset\\detection_normal_900_9_train_2048.mat')
        normal_900_9 = train_normal_900_9['detection_normal_900_9_train_2048']

        self.normal_data_900_9 = normal_900_9[0, 0:1843200]  # 样本数：900

        self.normal_data_900_9 = torch.from_numpy(self.normal_data_900_9)

        self.normal_data_900_9 = self.normal_data_900_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_12 = scio.loadmat('..\\dataset\\detection_normal_900_12_train_2048.mat')
        normal_900_12 = train_normal_900_12['detection_normal_900_12_train_2048']

        self.normal_data_900_12 = normal_900_12[0, 0:1843200]  # 样本数：900

        self.normal_data_900_12 = torch.from_numpy(self.normal_data_900_12)

        self.normal_data_900_12 = self.normal_data_900_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_15 = scio.loadmat('..\\dataset\\detection_normal_900_15_train_2048.mat')
        normal_900_15 = train_normal_900_15['detection_normal_900_15_train_2048']

        self.normal_data_900_15 = normal_900_15[0, 0:1843200]  # 样本数：900

        self.normal_data_900_15 = torch.from_numpy(self.normal_data_900_15)

        self.normal_data_900_15 = self.normal_data_900_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_18 = scio.loadmat('..\\dataset\\detection_normal_900_18_train_2048.mat')
        normal_900_18 = train_normal_900_18['detection_normal_900_18_train_2048']

        self.normal_data_900_18 = normal_900_18[0, 0:1843200]  # 样本数：900

        self.normal_data_900_18 = torch.from_numpy(self.normal_data_900_18)

        self.normal_data_900_18 = self.normal_data_900_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        self.x_data = [self.normal_data_1500_9, self.normal_data_1500_12, self.normal_data_1500_15,
                       self.normal_data_1500_18,
                       self.normal_data_1200_9, self.normal_data_1200_12, self.normal_data_1200_15,
                       self.normal_data_1200_18,
                       self.normal_data_900_9, self.normal_data_900_12, self.normal_data_900_15,
                       self.normal_data_900_18]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.x_data.shape[0])  # 计算标签数量
        y_data1 = 0 * np.ones(size)  # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        count_1500_9 = self.normal_data_1500_9.shape[0]
        count_1500_12 = self.normal_data_1500_12.shape[0]
        count_1500_15 = self.normal_data_1500_15.shape[0]
        count_1500_18 = self.normal_data_1500_18.shape[0]

        count_1200_9 = self.normal_data_1200_9.shape[0]
        count_1200_12 = self.normal_data_1200_12.shape[0]
        count_1200_15 = self.normal_data_1200_15.shape[0]
        count_1200_18 = self.normal_data_1200_18.shape[0]

        count_900_9 = self.normal_data_900_9.shape[0]
        count_900_12 = self.normal_data_900_12.shape[0]
        count_900_15 = self.normal_data_900_15.shape[0]
        count_900_18 = self.normal_data_900_18.shape[0]

        condition_1500_9 = [(1500, 9)] * count_1500_9  # 转速1500rpm, 压力9MPa
        condition_1500_12 = [(1500, 12)] * count_1500_12  # 转速1500rpm, 压力12MPa
        condition_1500_15 = [(1500, 15)] * count_1500_15  # 转速1500rpm, 压力15MPa
        condition_1500_18 = [(1500, 18)] * count_1500_18  # 转速1500rpm, 压力18MPa

        condition_1200_9 = [(1200, 9)] * count_1200_9  # 转速1200rpm, 压力9MPa
        condition_1200_12 = [(1200, 12)] * count_1200_12  # 转速1200rpm, 压力12MPa
        condition_1200_15 = [(1200, 15)] * count_1200_15  # 转速1200rpm, 压力15MPa
        condition_1200_18 = [(1200, 18)] * count_1200_18  # 转速1200rpm, 压力18MPa

        condition_900_9 = [(900, 9)] * count_900_9  # 转速900rpm, 压力9MPa
        condition_900_12 = [(900, 12)] * count_900_12  # 转速900rpm, 压力12MPa
        condition_900_15 = [(900, 15)] * count_900_15  # 转速900rpm, 压力15MPa
        condition_900_18 = [(900, 18)] * count_900_18  # 转速900rpm, 压力18MPa

        # 合并所有工况标签
        self.condition_data = condition_1500_9 + condition_1500_12 + condition_1500_15 + condition_1500_18 + \
                              condition_1200_9 + condition_1200_12 + condition_1200_15 + condition_1200_18 + \
                              condition_900_9 + condition_900_12 + condition_900_15 + condition_900_18

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('..\\dataset\\detection_normal_900_9_val_2048.mat')
        normal = val_normal['detection_normal_900_9_val_2048']

        self.normal_data = normal[0, 0:409600]  # 样本数：200

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('..\\dataset\\loose8067_12conditions\\detection_loose8067_900_9_val_2048.mat')
        loose8067 = val_loose8067['detection_loose8067_900_9_val_2048']

        self.loose8067 = loose8067[0, 0:409600]  # 样本数：200

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        # 所有验证数据都是1500rpm, 15MPa工况
        condition_normal = [(900, 9)] * size_normal  # 正常数据工况标签
        condition_loose = [(900, 9)] * size_loose  # 异常数据工况标签

        # 合并所有工况标签
        self.condition_data = condition_normal + condition_loose

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('..\\dataset\\detection_normal_900_9_test_2048.mat')
        normal = test_normal['detection_normal_900_9_test_2048']

        self.normal_data = normal[0, 0:409600]  # 样本数：200

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('..\\dataset\\loose8067_12conditions\\detection_loose8067_900_9_test_2048.mat')
        loose8067 = test_loose8067['detection_loose8067_900_9_test_2048']

        self.loose8067 = loose8067[0, 0:409600]  # 样本数：200

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        # 所有测试数据都是1500rpm, 15MPa工况
        condition_normal = [(900, 9)] * size_normal  # 正常数据工况标签
        condition_loose = [(900, 9)] * size_loose  # 异常数据工况标签

        # 合并所有工况标签
        self.condition_data = condition_normal + condition_loose

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)

# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)

# GAN模型参数
noise_dim = 512  # 噪声向量维度
wl = 2048  # 时间序列窗口长度


# GAN生成器
class Generator(nn.Module):
    def __init__(self, noise_dim=noise_dim, output_dim=wl):
        super(Generator, self).__init__()
        # 两层全连接层
        self.fc1 = nn.Linear(noise_dim, 1024)
        self.fc2 = nn.Linear(1024, output_dim)
        self.tanh = nn.Tanh()

    def forward(self, z):
        # z: [batch_size, noise_dim]
        h1 = self.tanh(self.fc1(z))  # [batch_size, 256]
        output = self.tanh(self.fc2(h1))  # [batch_size, 2048]
        return output


# GAN判别器
class Discriminator(nn.Module):
    def __init__(self, input_dim=wl):
        super(Discriminator, self).__init__()
        # 一维卷积层
        self.conv1d = nn.Conv1d(in_channels=1, out_channels=64, kernel_size=3, stride=1, padding=1)

        # 计算卷积后的特征维度
        conv_output_size = 64 * input_dim  # 64 channels * 2048 length

        # 两层全连接层
        self.fc1 = nn.Linear(conv_output_size, 256)
        self.fc2 = nn.Linear(256, 1)

        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        # x: [batch_size, 2048] 需要reshape为 [batch_size, 1, 2048] 用于Conv1d
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # [batch_size, 1, 2048]

        # 卷积层
        h1 = self.relu(self.conv1d(x))  # [batch_size, 64, 2048]
        h1 = h1.view(h1.size(0), -1)  # flatten: [batch_size, 64*2048]

        # 全连接层
        h2 = self.relu(self.fc1(h1))
        h2 = self.dropout(h2)
        output = self.sigmoid(self.fc2(h2))  # [batch_size, 1]

        return output


# GAN损失函数
def discriminator_loss(real_output, fake_output):
    """判别器损失：最大化对真实数据的判别能力，最小化对生成数据的判别能力"""
    real_loss = F.binary_cross_entropy(real_output, torch.ones_like(real_output))
    fake_loss = F.binary_cross_entropy(fake_output, torch.zeros_like(fake_output))
    total_loss = real_loss + fake_loss
    return total_loss


def generator_loss(fake_output):
    """生成器损失：最大化判别器对生成数据的判别错误"""
    return F.binary_cross_entropy(fake_output, torch.ones_like(fake_output))


# 训练 GAN 模型
generator = Generator(noise_dim, wl).to(device)
discriminator = Discriminator(wl).to(device)

# 优化器
g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))

gan_epochs = 6

# 初始化记录损失的列表
all_losses = {
    'generator_loss': [],
    'discriminator_loss': [],
    'total_loss': []
}

for gan_epoch in range(gan_epochs):
    print(f"GAN Epoch {gan_epoch} - GAN Model Training")
    # 将模型设置为训练模式
    generator.train()
    discriminator.train()

    epoch_losses = {
        'generator_loss': [],
        'discriminator_loss': [],
        'total_loss': []
    }

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        batch_size = inputs.size(0)

        # 将输入数据flatten为[batch_size, 2048]
        real_data = inputs.view(batch_size, -1)

        # ===== 训练判别器 =====
        d_optimizer.zero_grad()

        # 真实数据
        real_output = discriminator(real_data)

        # 生成假数据
        noise = torch.randn(batch_size, noise_dim).to(device)
        fake_data = generator(noise)
        fake_output = discriminator(fake_data.detach())  # detach避免生成器梯度传播

        # 判别器损失
        d_loss = discriminator_loss(real_output, fake_output)
        d_loss.backward()
        d_optimizer.step()

        # ===== 训练生成器 =====
        g_optimizer.zero_grad()

        # 重新生成假数据（不detach，允许梯度传播）
        fake_output = discriminator(fake_data)
        g_loss = generator_loss(fake_output)
        g_loss.backward()
        g_optimizer.step()

        # 记录损失
        total_loss = g_loss + d_loss
        epoch_losses['generator_loss'].append(g_loss.item())
        epoch_losses['discriminator_loss'].append(d_loss.item())
        epoch_losses['total_loss'].append(total_loss.item())

        # 每 32 个批次打印当前损失
        if idx % 32 == 0:
            print(f"G_loss: {g_loss.item():.3f}, D_loss: {d_loss.item():.3f} in Step {idx}")

        # training curve
        global_iter_num_train = gan_epoch * len(train_loader) + idx + 1
        GAN_train_wind.line([[g_loss.item(), d_loss.item()]], [global_iter_num_train],
                           win='GAN_train', update='append')

    # 计算并保存每个epoch的平均损失
    for key in epoch_losses:
        epoch_avg = np.mean(epoch_losses[key])
        all_losses[key].append(epoch_avg)
        print(f"Epoch {gan_epoch} average {key}: {epoch_avg:.6f}")

    # 保存损失记录到文件
    loss_df = pd.DataFrame(all_losses)
    loss_df.to_csv('gan_training_losses.csv', index=False)

# GAN训练完成
print("\n=== GAN Training Completed ===")
print("GAN architecture with adversarial training")
print("=" * 50)


# 新颖性检测结果评估
# ROC曲线 & AUC
def evaluate_roc_auc(scores, labels):
    fpr, tpr, thresholds = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {roc_auc:0.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    # 将ROC曲线的数据保存为CSV文件
    roc_data = pd.DataFrame({'False Positive Rate': fpr, 'True Positive Rate': tpr, 'Thresholds': thresholds})
    roc_data.to_csv('roc_data.csv', index=False)

    return roc_auc


# 分位数阈值计算函数
def calculate_quantile_threshold(scores, confidence=0.995, method='percentile'):
    """
    使用分位数方法计算异常检测阈值

    Args:
        scores: 异常分数数组
        confidence: 置信度 (0.995 表示99.5%分位数)
        method: 计算方法 ('percentile' 或 'quantile')

    Returns:
        threshold: 计算得到的阈值
        stats: 统计信息字典
    """
    scores_array = np.array(scores)

    if method == 'percentile':
        # 使用numpy percentile方法
        threshold = np.percentile(scores_array, confidence * 100)
    elif method == 'quantile':
        # 使用numpy quantile方法（更现代的API）
        threshold = np.quantile(scores_array, confidence)
    else:
        raise ValueError(f"Unknown method: {method}")

    # 计算详细统计信息
    stats = {
        'mean': np.mean(scores_array),
        'std': np.std(scores_array),
        'median': np.median(scores_array),
        'q25': np.percentile(scores_array, 25),
        'q75': np.percentile(scores_array, 75),
        'q90': np.percentile(scores_array, 90),
        'q95': np.percentile(scores_array, 95),
        'q99': np.percentile(scores_array, 99),
        'q99_5': np.percentile(scores_array, 99.5),
        'q99_9': np.percentile(scores_array, 99.9),
        'min': np.min(scores_array),
        'max': np.max(scores_array),
        'count': len(scores_array)
    }

    return threshold, stats


def compare_quantile_thresholds(scores, test_scores, test_labels, confidence_levels=None):
    """
    比较不同分位数阈值的性能

    Args:
        scores: 训练数据的异常分数（用于计算阈值）
        test_scores: 测试数据的异常分数
        test_labels: 测试数据的真实标签
        confidence_levels: 要测试的置信度水平列表

    Returns:
        results: 包含各个阈值性能的字典
    """
    if confidence_levels is None:
        confidence_levels = [0.90, 0.95, 0.99, 0.995, 0.999]

    results = {}

    print("\n====== Quantile Threshold Comparison ======")
    print(f"{'Confidence':<12} {'Threshold':<12} {'Accuracy':<10} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'FPR':<10}")
    print("=" * 80)

    for conf in confidence_levels:
        # 计算阈值
        threshold, _ = calculate_quantile_threshold(scores, confidence=conf)

        # 进行预测
        predictions = [int(score > threshold) for score in test_scores]

        # 计算性能指标
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

        accuracy = accuracy_score(test_labels, predictions)
        precision = precision_score(test_labels, predictions, zero_division=0)
        recall = recall_score(test_labels, predictions, zero_division=0)
        f1 = f1_score(test_labels, predictions, zero_division=0)

        # 计算FPR
        cm = confusion_matrix(test_labels, predictions)
        tn, fp, fn, tp = cm.ravel()
        fpr = fp / (fp + tn) if (fp + tn) > 0 else 0.0

        # 保存结果
        results[conf] = {
            'threshold': threshold,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'fpr': fpr,
            'confusion_matrix': cm.tolist()
        }

        # 打印结果
        print(f"{conf:<12.3f} {threshold:<12.6f} {accuracy:<10.4f} {precision:<10.4f} {recall:<10.4f} {f1:<10.4f} {fpr:<10.4f}")

    print("=" * 80)

    # 找到最佳F1分数对应的置信度
    best_conf = max(confidence_levels, key=lambda c: results[c]['f1_score'])
    print(f"\n🏆 Best F1-Score: {results[best_conf]['f1_score']:.4f} at confidence {best_conf:.3f}")
    print(f"   Corresponding threshold: {results[best_conf]['threshold']:.6f}")

    return results


# GAN异常检测函数
def gan_anomaly_score(discriminator, data):
    """
    使用GAN判别器计算异常分数
    Args:
        discriminator: 训练好的判别器模型
        data: 输入数据 [batch_size, 2048]
    Returns:
        anomaly_scores: 异常分数，值越低越可能是异常
    """
    discriminator.eval()
    with torch.no_grad():
        # 将数据flatten为[batch_size, 2048]
        if len(data.shape) == 4:  # [B, C, H, W]
            data = data.view(data.size(0), -1)

        # 获取判别器分数
        disc_scores = discriminator(data)  # [batch_size, 1]
        disc_scores = disc_scores.squeeze()  # [batch_size]

        # 判别器分数越高表示越像真实数据（正常），越低表示越像伪造数据（异常）
        # 因此异常分数 = 1 - 判别器分数
        anomaly_scores = 1.0 - disc_scores

        return anomaly_scores


# 推理过程 - GAN全局统一阈值设置
discriminator.eval()
generator.eval()

# 设置随机种子以确保结果可重复
seed = 42
torch_generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
torch_generator.manual_seed(seed)

# GAN模型：收集所有训练数据的异常分数用于全局阈值计算
all_scores = []

with torch.no_grad():
    for data in train_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # 计算GAN异常分数
        anomaly_scores = gan_anomaly_score(discriminator, inputs)

        # 收集所有异常分数用于全局阈值计算
        all_scores.extend(anomaly_scores.cpu().numpy())

    # GAN模型：计算全局统一阈值（使用分位数方法）
    confidence = 0.995  # 置信度（99.5%分位数）

    print("====== GAN Model: Global Threshold Calculation (Quantile Method) ======")

    # 使用新的分位数阈值计算函数
    global_threshold, threshold_stats = calculate_quantile_threshold(
        scores=all_scores,
        confidence=confidence,
        method='percentile'
    )

    print(f"Global threshold (quantile method): {global_threshold:.6f}")
    print(f"Statistics: μ={threshold_stats['mean']:.6f}, σ={threshold_stats['std']:.6f}, median={threshold_stats['median']:.6f}")
    print(f"Quartiles: Q25={threshold_stats['q25']:.6f}, Q75={threshold_stats['q75']:.6f}")
    print(f"Key percentiles: Q90={threshold_stats['q90']:.6f}, Q95={threshold_stats['q95']:.6f}, Q99={threshold_stats['q99']:.6f}")
    print(f"High percentiles: Q99.5={threshold_stats['q99_5']:.6f}, Q99.9={threshold_stats['q99_9']:.6f}")
    print(f"Range: [{threshold_stats['min']:.6f}, {threshold_stats['max']:.6f}]")
    print(f"Total training samples: {threshold_stats['count']}")
    print("=" * 50)

    # 保存全局阈值信息
    threshold_data = {
        "global_stats": threshold_stats,  # 使用详细的统计信息
        "threshold_info": {
            "threshold": float(global_threshold),
            "confidence": float(confidence),
            "threshold_method": "quantile_percentile",  # 标明使用分位数方法
            "percentile": float(confidence * 100)  # 对应的百分位数
        },
        "model_type": "gan_discriminator"
    }

    with open("GAN_global_threshold.json", "w") as f:
        json.dump(threshold_data, f, indent=2)

    print("GAN global threshold saved to 'GAN_global_threshold.json'")

    # 额外输出：显示不同分位数对应的阈值
    print("\n📊 Different Quantile Thresholds:")
    print("=" * 50)
    print(f"{'Percentile':<12} {'Threshold':<15} {'Description':<20}")
    print("=" * 50)
    print(f"{'90%':<12} {threshold_stats['q90']:<15.6f} {'Conservative':<20}")
    print(f"{'95%':<12} {threshold_stats['q95']:<15.6f} {'Moderate':<20}")
    print(f"{'99%':<12} {threshold_stats['q99']:<15.6f} {'Strict':<20}")
    print(f"{'99.5%':<12} {threshold_stats['q99_5']:<15.6f} {'Very Strict (Used)':<20}")
    print(f"{'99.9%':<12} {threshold_stats['q99_9']:<15.6f} {'Extremely Strict':<20}")
    print("=" * 50)


# 加载全局阈值信息
try:
    with open("GAN_global_threshold.json", "r") as f:
        threshold_data = json.load(f)

    # 兼容新旧数据格式
    if "threshold_info" in threshold_data:
        # 新格式
        global_threshold = threshold_data["threshold_info"]["threshold"]
        confidence = threshold_data["threshold_info"]["confidence"]
        threshold_method = threshold_data["threshold_info"]["threshold_method"]
        percentile = threshold_data["threshold_info"].get("percentile", confidence * 100)
    else:
        # 旧格式兼容
        global_threshold = threshold_data["global_stats"]["threshold"]
        confidence = threshold_data.get("confidence", 0.995)
        threshold_method = threshold_data.get("threshold_method", "normal_distribution")
        percentile = confidence * 100

    print("====== Loaded GAN Global Threshold ======")
    print(f"Global threshold: {global_threshold:.6f}")
    print(f"Confidence: {confidence}")
    print(f"Threshold method: {threshold_method}")
    if threshold_method.startswith("quantile"):
        print(f"Percentile: {percentile:.1f}%")
    print("=" * 45)

except FileNotFoundError:
    print("GAN threshold file not found. Please run threshold calculation first.")
    # 设置默认值以防文件不存在
    global_threshold = 0.5


# 推理过程 - GAN全局阈值新颖性检测
discriminator.eval()
generator.eval()

# 设置随机种子以确保结果可重复
seed = 42
torch_generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
torch_generator.manual_seed(seed)

# 收集测试结果
test_scores = []
test_labels = []

with torch.no_grad():
    for data in val_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # 计算GAN异常分数
        anomaly_scores = gan_anomaly_score(discriminator, inputs)

        # 收集测试数据（不按工况分组）
        for score, label in zip(anomaly_scores, labels):
            test_scores.append(score.item())
            test_labels.append(label.item())

# GAN模型：使用全局阈值进行预测
print("====== GAN Model: Global Threshold Anomaly Detection Results ======")

# 仅使用全局阈值进行预测
all_predictions = []  # 存储使用全局阈值判断是否为异常的结果

for score in test_scores:
    # 进行预测
    pred = score > global_threshold  # 判断当前样本的异常分数是否超过了全局阈值
    all_predictions.append(int(pred))  # 储存预测结果

# 首先进行多个分位数阈值的性能比较
print("\n🔍 Comparing Different Quantile Thresholds Performance...")

# 重新加载训练数据的异常分数用于阈值比较
train_scores_for_comparison = []
with torch.no_grad():
    for data in train_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        anomaly_scores = gan_anomaly_score(discriminator, inputs)
        train_scores_for_comparison.extend(anomaly_scores.cpu().numpy())

# 比较不同分位数阈值的性能
quantile_comparison_results = compare_quantile_thresholds(
    scores=train_scores_for_comparison,
    test_scores=test_scores,
    test_labels=test_labels,
    confidence_levels=[0.90, 0.95, 0.99, 0.995, 0.999]
)

# 计算性能指标
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

# 全局阈值性能（使用当前设定的99.5%分位数阈值）
gan_auc = evaluate_roc_auc(test_scores, test_labels)
gan_accuracy = accuracy_score(test_labels, all_predictions)
gan_precision = precision_score(test_labels, all_predictions)
gan_recall = recall_score(test_labels, all_predictions)
gan_f1 = f1_score(test_labels, all_predictions)

# 计算FPR
cm_gan = confusion_matrix(test_labels, all_predictions)
tn_gan, fp_gan, fn_gan, tp_gan = cm_gan.ravel()
gan_fpr = fp_gan / (fp_gan + tn_gan) if (fp_gan + tn_gan) > 0 else 0.0

# 打印GAN模型性能
print("\n📊 GAN Model Performance (Using Quantile Threshold):")
print("=" * 60)
print(f"{'Metric':<20} {'Value':<20} {'Note':<20}")
print("=" * 60)
print(f"{'AUC':<20} {gan_auc:<20.4f} {'ROC Area':<20}")
print(f"{'Accuracy':<20} {gan_accuracy:<20.4f} {'Overall Accuracy':<20}")
print(f"{'Precision':<20} {gan_precision:<20.4f} {'Anomaly Precision':<20}")
print(f"{'Recall':<20} {gan_recall:<20.4f} {'Anomaly Recall':<20}")
print(f"{'F1-Score':<20} {gan_f1:<20.4f} {'Harmonic Mean':<20}")
print(f"{'FPR':<20} {gan_fpr:<20.4f} {'False Positive Rate':<20}")
print("=" * 60)
print(f"Threshold Method: Quantile (99.5% percentile)")
print(f"Threshold Value: {global_threshold:.6f}")

# 详细分析
print("\n🔍 GAN Model Detailed Analysis:")
print("\nGlobal Threshold Results:")
cm_gan = confusion_matrix(test_labels, all_predictions)
print("Confusion Matrix:")
print(cm_gan)
print("Classification Report:")
print(classification_report(test_labels, all_predictions))

# 绘制GAN异常分数分布柱状图
scores_array = np.array(test_scores)
true_labels_array = np.array(test_labels)
normal_scores = scores_array[true_labels_array == 0]
anomaly_scores = scores_array[true_labels_array == 1]

# 使用改进的绘图函数，但只显示全局阈值
fig = plot_error_distribution_improved(
    normal_scores=normal_scores,
    anomaly_scores=anomaly_scores,
    global_threshold=global_threshold,
    condition_thresholds=None,  # GAN模型：不显示工况特定阈值
    title='GAN Global Threshold Anomaly Score Distribution'
)
plt.show()

print(f"\n✨ GAN Model Implementation Complete!")
print(f" Threshold method: Quantile (99.5% percentile)")
print(f" Global threshold: {global_threshold:.6f}")
print(f" F1-Score: {gan_f1:.4f}")

# 保存分位数阈值比较结果
print("\n💾 Saving quantile threshold comparison results...")
quantile_comparison_df = pd.DataFrame([
    {
        'confidence': conf,
        'percentile': conf * 100,
        'threshold': results['threshold'],
        'accuracy': results['accuracy'],
        'precision': results['precision'],
        'recall': results['recall'],
        'f1_score': results['f1_score'],
        'fpr': results['fpr']
    }
    for conf, results in quantile_comparison_results.items()
])
quantile_comparison_df.to_csv('GAN_quantile_threshold_comparison.csv', index=False)
print("📊 Quantile threshold comparison saved to 'GAN_quantile_threshold_comparison.csv'")

# 保存GAN异常分数分布图的数据
print("\n📊 Saving GAN anomaly score distribution data...")

# 计算直方图数据
n_samples = len(normal_scores) + len(anomaly_scores)
n_bins = min(50, max(20, int(np.sqrt(n_samples))))
min_score = min(min(normal_scores), min(anomaly_scores))
max_score = max(max(normal_scores), max(anomaly_scores))
common_bins = np.linspace(min_score, max_score, n_bins + 1)

# 计算直方图统计
normal_hist_counts, _ = np.histogram(normal_scores, bins=common_bins, density=True)
anomaly_hist_counts, _ = np.histogram(anomaly_scores, bins=common_bins, density=True)
bin_centers = (common_bins[:-1] + common_bins[1:]) / 2  # 计算bin中心点

# 计算核密度估计数据
from scipy.stats import gaussian_kde
x_grid = np.linspace(min_score, max_score, 200)

normal_kde_values = None
anomaly_kde_values = None
if len(normal_scores) > 1:
    kde_normal = gaussian_kde(normal_scores, bw_method=0.3)
    normal_kde_values = kde_normal(x_grid)

if len(anomaly_scores) > 1:
    kde_anomaly = gaussian_kde(anomaly_scores, bw_method=0.3)
    anomaly_kde_values = kde_anomaly(x_grid)

# 准备保存的数据字典
distribution_data = {
    'metadata': {
        'total_samples': n_samples,
        'normal_samples': len(normal_scores),
        'anomaly_samples': len(anomaly_scores),
        'n_bins': n_bins,
        'score_range': [float(min_score), float(max_score)],
        'global_threshold': float(global_threshold) if global_threshold is not None else None
    },
    'histogram': {
        'bin_edges': common_bins.tolist(),
        'bin_centers': bin_centers.tolist(),
        'normal_density': normal_hist_counts.tolist(),
        'anomaly_density': anomaly_hist_counts.tolist()
    },
    'kde': {
        'x_grid': x_grid.tolist(),
        'normal_kde': normal_kde_values.tolist() if normal_kde_values is not None else None,
        'anomaly_kde': anomaly_kde_values.tolist() if anomaly_kde_values is not None else None
    },
    'raw_scores': {
        'normal_scores': normal_scores.tolist(),
        'anomaly_scores': anomaly_scores.tolist()
    },
    'thresholds': {
        'global_threshold': float(global_threshold) if global_threshold is not None else None,
        'condition_thresholds': {},  # GAN模型：无工况特定阈值
        'unique_condition_thresholds': []  # GAN模型：无工况特定阈值
    },
    'statistics': {
        'normal': {
            'mean': float(np.mean(normal_scores)),
            'std': float(np.std(normal_scores)),
            'min': float(np.min(normal_scores)),
            'max': float(np.max(normal_scores))
        },
        'anomaly': {
            'mean': float(np.mean(anomaly_scores)),
            'std': float(np.std(anomaly_scores)),
            'min': float(np.min(anomaly_scores)),
            'max': float(np.max(anomaly_scores))
        }
    }
}

# 保存为JSON文件
import json
with open('GAN_anomaly_score_distribution_data.json', 'w') as f:
    json.dump(distribution_data, f, indent=2)

# GAN模型：保存为CSV文件（便于Excel等软件使用）
import pandas as pd

# 直方图数据
hist_df = pd.DataFrame({
    'bin_centers': bin_centers,
    'bin_edges_left': common_bins[:-1],
    'bin_edges_right': common_bins[1:],
    'normal_density': normal_hist_counts,
    'anomaly_density': anomaly_hist_counts
})
hist_df.to_csv('GAN_histogram_data.csv', index=False)

# KDE数据
kde_df = pd.DataFrame({
    'x_grid': x_grid,
    'normal_kde': normal_kde_values if normal_kde_values is not None else [None] * len(x_grid),
    'anomaly_kde': anomaly_kde_values if anomaly_kde_values is not None else [None] * len(x_grid)
})
kde_df.to_csv('GAN_kde_data.csv', index=False)

# 原始分数数据
max_len = max(len(normal_scores), len(anomaly_scores))
normal_padded = list(normal_scores) + [None] * (max_len - len(normal_scores))
anomaly_padded = list(anomaly_scores) + [None] * (max_len - len(anomaly_scores))

scores_df = pd.DataFrame({
    'normal_scores': normal_padded,
    'anomaly_scores': anomaly_padded
})
scores_df.to_csv('GAN_raw_scores_data.csv', index=False)

# GAN模型：阈值数据（仅全局阈值）
thresholds_data = []
if global_threshold is not None:
    thresholds_data.append({'type': 'global', 'value': global_threshold, 'condition': 'all'})

if thresholds_data:
    thresholds_df = pd.DataFrame(thresholds_data)
    thresholds_df.to_csv('GAN_thresholds_data.csv', index=False)

print("✅ GAN model distribution data saved successfully:")
print("  📄 GAN_anomaly_score_distribution_data.json - Complete data in JSON format")
print("  📊 GAN_histogram_data.csv - Histogram bin data")
print("  📈 GAN_kde_data.csv - Kernel density estimation data")
print("  📋 GAN_raw_scores_data.csv - Raw anomaly scores")
print("  🎯 GAN_thresholds_data.csv - Global threshold value")
print("  💡 Use these files with other plotting software (e.g., Origin, MATLAB, R, etc.)")

