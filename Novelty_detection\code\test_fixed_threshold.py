#!/usr/bin/env python3
"""
测试固定阈值0.5的异常检测逻辑
"""

import numpy as np
import torch
import torch.nn as nn

# 模拟判别器输出（sigmoid激活函数，输出范围[0,1]）
def simulate_discriminator_output():
    """模拟判别器的输出"""
    # 正常样本：判别器输出应该接近1（认为是真实数据）
    normal_disc_outputs = np.random.beta(8, 2, 100)  # 偏向1的分布
    
    # 异常样本：判别器输出应该接近0（认为是伪造数据）
    anomaly_disc_outputs = np.random.beta(2, 8, 100)  # 偏向0的分布
    
    return normal_disc_outputs, anomaly_disc_outputs

def calculate_anomaly_scores(disc_outputs):
    """计算异常分数：1 - 判别器输出"""
    return 1.0 - disc_outputs

def fixed_threshold_detection(anomaly_scores, threshold=0.5):
    """使用固定阈值进行异常检测"""
    predictions = anomaly_scores > threshold
    return predictions.astype(int)

# 测试
print("=" * 60)
print("测试固定阈值0.5的异常检测逻辑")
print("=" * 60)

# 1. 模拟数据
normal_disc_out, anomaly_disc_out = simulate_discriminator_output()

print(f"正常样本判别器输出范围: [{normal_disc_out.min():.3f}, {normal_disc_out.max():.3f}]")
print(f"异常样本判别器输出范围: [{anomaly_disc_out.min():.3f}, {anomaly_disc_out.max():.3f}]")

# 2. 计算异常分数
normal_anomaly_scores = calculate_anomaly_scores(normal_disc_out)
anomaly_anomaly_scores = calculate_anomaly_scores(anomaly_disc_out)

print(f"正常样本异常分数范围: [{normal_anomaly_scores.min():.3f}, {normal_anomaly_scores.max():.3f}]")
print(f"异常样本异常分数范围: [{anomaly_anomaly_scores.min():.3f}, {anomaly_anomaly_scores.max():.3f}]")

# 3. 使用固定阈值0.5进行检测
all_anomaly_scores = np.concatenate([normal_anomaly_scores, anomaly_anomaly_scores])
true_labels = np.concatenate([np.zeros(len(normal_anomaly_scores)), np.ones(len(anomaly_anomaly_scores))])

predictions = fixed_threshold_detection(all_anomaly_scores, threshold=0.5)

# 4. 计算性能
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

accuracy = accuracy_score(true_labels, predictions)
precision = precision_score(true_labels, predictions, zero_division=0)
recall = recall_score(true_labels, predictions, zero_division=0)
f1 = f1_score(true_labels, predictions, zero_division=0)

cm = confusion_matrix(true_labels, predictions)
tn, fp, fn, tp = cm.ravel()
fpr = fp / (fp + tn) if (fp + tn) > 0 else 0.0

print("\n" + "=" * 60)
print("固定阈值0.5检测结果:")
print("=" * 60)
print(f"阈值: 0.5")
print(f"检测逻辑: anomaly_score > 0.5 → 异常")
print(f"准确率: {accuracy:.4f}")
print(f"精确率: {precision:.4f}")
print(f"召回率: {recall:.4f}")
print(f"F1分数: {f1:.4f}")
print(f"误报率: {fpr:.4f}")

print(f"\n混淆矩阵:")
print(f"TN={tn}, FP={fp}")
print(f"FN={fn}, TP={tp}")

print(f"\n样本分析:")
normal_detected_as_anomaly = np.sum(normal_anomaly_scores > 0.5)
anomaly_detected_as_anomaly = np.sum(anomaly_anomaly_scores > 0.5)
print(f"正常样本被误判为异常: {normal_detected_as_anomaly}/{len(normal_anomaly_scores)} ({normal_detected_as_anomaly/len(normal_anomaly_scores)*100:.1f}%)")
print(f"异常样本被正确检测: {anomaly_detected_as_anomaly}/{len(anomaly_anomaly_scores)} ({anomaly_detected_as_anomaly/len(anomaly_anomaly_scores)*100:.1f}%)")

print("=" * 60)
print("测试完成！固定阈值0.5的逻辑验证正确。")
print("=" * 60)
